# Authentication Flow Testing Guide

## Overview
The new authentication system implements an OTP-first flow where users can login with just their email or phone number, receive an OTP, and get authenticated without needing a password initially.

## Authentication Flow

### 1. Login (Send OTP)
```bash
# Login with email
curl -X POST http://localhost:3100/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Login with phone
curl -X POST http://localhost:3100/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"phone": "+1234567890"}'
```

**Response:**
```json
{
  "message": "OTP <NAME_EMAIL>",
  "isNewUser": true,
  "otp": "123456"
}
```

### 2. Verify OTP (Get Access Token)
```bash
# Verify OTP with email
curl -X POST http://localhost:3100/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "otp": "123456"}'

# Verify OTP with phone
curl -X POST http://localhost:3100/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"phone": "+1234567890", "otp": "123456"}'
```

**Response:**
```json
{
  "message": "Login successful",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "phone": null,
    "username": "user",
    "firstName": null,
    "lastName": null,
    "role": "USER",
    "isVerified": true
  }
}
```

### 3. Complete Registration (Authenticated Required)
```bash
curl -X POST http://localhost:3100/api/auth/register \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "username": "johndoe",
    "firstName": "John",
    "lastName": "Doe",
    "gender": "MALE",
    "ageRange": "TWENTY_FIVE_TO_THIRTY_FOUR",
    "country": "USA",
    "bio": "Software developer"
  }'
```

### 4. Set Password (For Withdrawals)
```bash
curl -X POST http://localhost:3100/api/auth/set-password \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{"password": "securepassword123"}'
```

### 5. Access Protected Routes
```bash
# Get user profile
curl -X GET http://localhost:3100/api/user/profile \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Update profile
curl -X PUT http://localhost:3100/api/user/profile \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "firstName": "Jane",
    "bio": "Updated bio"
  }'

# Admin-only endpoint (requires ADMIN role)
curl -X GET http://localhost:3100/api/user/admin-only \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Password Reset Flow

### 1. Request Password Reset
```bash
curl -X POST http://localhost:3100/api/auth/forgot-password \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

### 2. Reset Password with OTP
```bash
curl -X POST http://localhost:3100/api/auth/reset-password \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "otp": "123456",
    "newPassword": "newpassword123"
  }'
```

## Key Features

1. **OTP-First Authentication**: Users don't need passwords to login initially
2. **New User Detection**: System automatically detects if user is new or existing
3. **Protected Registration**: Registration endpoint requires authentication
4. **Optional Passwords**: Passwords only required for sensitive operations
5. **Flexible Login**: Support both email and phone number login
6. **JWT Tokens**: 7-day expiration for access tokens
7. **Role-Based Access**: Support for USER, ADMIN, COMPANY_OWNER roles

## Environment Variables

Make sure to set these in your `.env` file:
```
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
DATABASE_URL=mysql://username:password@localhost:3306/venchazdb
NODE_ENV=development
```

## Notes

- In development mode, OTP is returned in the response for testing
- In production, OTP should only be sent via email/SMS
- Email and SMS services need to be implemented (currently commented out)
- Users can have either email or phone (or both) as login methods
