# New Authentication Flow Testing Guide

## Overview
The authentication system now uses OTP-based login with phone/email. Here's how to test the new flow:

## 1. Login/Send OTP
**POST** `/api/auth/login`

```json
{
  "email": "<EMAIL>"
}
```
OR
```json
{
  "phone": "+1234567890"
}
```

**Response:**
```json
{
  "message": "O<PERSON> sent successfully",
  "isNewUser": true,
  "otp": "123456"  // Only in development
}
```

## 2. Verify OTP
**POST** `/api/auth/verify-otp`

```json
{
  "email": "<EMAIL>",
  "otp": "123456",
  "isNewUser": true,
  "countryCode": "US"
}
```

**Response:**
```json
{
  "message": "OTP verified successfully",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "isNewUser": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "phone": null,
    "username": null,
    "firstName": null,
    "lastName": null,
    "country": "US",
    "role": "USER",
    "isVerified": true
  }
}
```

## 3. Complete Registration (Authenticated)
**POST** `/api/auth/register`
**Headers:** `Authorization: Bearer <accessToken>`

```json
{
  "username": "johndoe",
  "firstName": "John",
  "lastName": "Doe",
  "gender": "MALE",
  "ageRange": "TWENTIES",
  "bio": "Hello world!"
}
```

## 4. Set Password (For Withdrawals)
**POST** `/api/auth/set-password`
**Headers:** `Authorization: Bearer <accessToken>`

```json
{
  "password": "securePassword123"
}
```

## 5. Get User Profile
**GET** `/api/user/profile`
**Headers:** `Authorization: Bearer <accessToken>`

## Key Changes:
1. **Login** now only requires email OR phone, sends OTP
2. **Registration** is now authenticated and optional
3. **Password** is only required for withdrawals
4. **OTP verification** creates the user session and JWT token
5. **Country code** can be set during OTP verification for new users
