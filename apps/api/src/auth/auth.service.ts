import { Injectable, ConflictException, UnauthorizedException, BadRequestException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { authenticator } from 'otplib';
import { DatabaseService } from '../database/database.service';
import { RegisterDto, LoginDto, VerifyOtpDto, ForgotPasswordDto, ResetPasswordDto } from './dto';
import { User } from '../../generated/prisma';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly jwtService: JwtService,
  ) {}

  async register(registerDto: RegisterDto) {
    const { email, username, password, ...userData } = registerDto;

    // Check if user already exists
    const existingUser = await this.databaseService.user.findFirst({
      where: {
        OR: [
          { email },
          { username }
        ]
      }
    });

    if (existingUser) {
      if (existingUser.email === email) {
        throw new ConflictException('Email already exists');
      }
      if (existingUser.username === username) {
        throw new ConflictException('Username already exists');
      }
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    // Generate OTP
    const otp = this.generateOtp();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create user
    const user = await this.databaseService.user.create({
      data: {
        email,
        username,
        password: hashedPassword,
        otp,
        otpExpires,
        ...userData,
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        isVerified: true,
        createdAt: true,
      }
    });

    // TODO: Send OTP email
    // await this.sendOtpEmail(email, otp);

    return {
      message: 'Registration successful. Please verify your email with the OTP sent.',
      user,
      // For development, return OTP (remove in production)
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  }

  async login(loginDto: LoginDto) {
    const { email, username, password } = loginDto;

    if (!email && !username) {
      throw new BadRequestException('Email or username is required');
    }

    // Find user
    const user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          username ? { username } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is verified
    if (!user.isVerified) {
      throw new UnauthorizedException('Please verify your email first');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new UnauthorizedException('Account is deactivated');
    }

    // Update last login
    await this.databaseService.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Generate JWT token
    const payload = { 
      sub: user.id, 
      email: user.email, 
      username: user.username,
      role: user.role 
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      message: 'Login successful',
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isVerified: user.isVerified,
      }
    };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const { email, otp } = verifyOtpDto;

    const user = await this.databaseService.user.findUnique({
      where: { email }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.otp || !user.otpExpires) {
      throw new BadRequestException('No OTP found for this user');
    }

    if (user.otpExpires < new Date()) {
      throw new BadRequestException('OTP has expired');
    }

    if (user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // Update user as verified and clear OTP
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        isVerified: true,
        otp: null,
        otpExpires: null,
      }
    });

    return {
      message: 'Email verified successfully',
    };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const { email } = forgotPasswordDto;

    const user = await this.databaseService.user.findUnique({
      where: { email }
    });

    if (!user) {
      // Don't reveal if email exists or not
      return {
        message: 'If the email exists, a password reset OTP has been sent.',
      };
    }

    // Generate OTP
    const otp = this.generateOtp();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        otp,
        otpExpires,
      }
    });

    // TODO: Send OTP email
    // await this.sendOtpEmail(email, otp);

    return {
      message: 'If the email exists, a password reset OTP has been sent.',
      // For development, return OTP (remove in production)
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { email, otp, newPassword } = resetPasswordDto;

    const user = await this.databaseService.user.findUnique({
      where: { email }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.otp || !user.otpExpires) {
      throw new BadRequestException('No OTP found for this user');
    }

    if (user.otpExpires < new Date()) {
      throw new BadRequestException('OTP has expired');
    }

    if (user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    // Update password and clear OTP
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        otp: null,
        otpExpires: null,
      }
    });

    return {
      message: 'Password reset successfully',
    };
  }

  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // TODO: Implement email service
  // private async sendOtpEmail(email: string, otp: string) {
  //   // Implementation for sending OTP email
  // }
}
