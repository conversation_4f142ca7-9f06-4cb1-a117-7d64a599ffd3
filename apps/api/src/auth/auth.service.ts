import { Injectable, ConflictException, BadRequestException, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { DatabaseService } from '../database/database.service';
import { RegisterDto, LoginDto, VerifyOtpDto, SetPasswordDto } from './dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly jwtService: JwtService,
  ) {}

  async login(loginDto: LoginDto) {
    const { email, phone } = loginDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    // Check if user exists
    let user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    let isNewUser = false;

    // If user doesn't exist, create a new user record
    if (!user) {
      isNewUser = true;
      user = await this.databaseService.user.create({
        data: {
          email: email || null,
          phone: phone || null,
          isVerified: false,
        }
      });
    }

    // Generate OTP
    const otp = this.generateOtp();
    const otpExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Update user with OTP
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        otp,
        otpExpires,
      }
    });

    // TODO: Send OTP via email or SMS
    if (email) {
      // await this.sendOtpEmail(email, otp);
    } else if (phone) {
      // await this.sendOtpSms(phone, otp);
    }

    return {
      message: 'OTP sent successfully',
      isNewUser,
      // For development, return OTP (remove in production)
      otp: process.env.NODE_ENV === 'development' ? otp : undefined,
    };
  }

  async register(registerDto: RegisterDto, userId: number) {
    const { username, ...userData } = registerDto;

    // Get the authenticated user
    const user = await this.databaseService.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if username already exists (if provided)
    if (username) {
      const existingUser = await this.databaseService.user.findFirst({
        where: {
          username,
          id: { not: userId }
        }
      });

      if (existingUser) {
        throw new ConflictException('Username already exists');
      }
    }

    // Update user profile
    const updatedUser = await this.databaseService.user.update({
      where: { id: userId },
      data: {
        username,
        ...userData,
      },
      select: {
        id: true,
        email: true,
        phone: true,
        username: true,
        firstName: true,
        lastName: true,
        gender: true,
        ageRange: true,
        country: true,
        bio: true,
        isVerified: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    return {
      message: 'Profile updated successfully',
      user: updatedUser,
    };
  }

  async verifyOtp(verifyOtpDto: VerifyOtpDto) {
    const { email, phone, otp } = verifyOtpDto;

    if (!email && !phone) {
      throw new BadRequestException('Email or phone number is required');
    }

    const user = await this.databaseService.user.findFirst({
      where: {
        OR: [
          email ? { email } : {},
          phone ? { phone } : {},
        ].filter(condition => Object.keys(condition).length > 0)
      }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    if (!user.otp || !user.otpExpires) {
      throw new BadRequestException('No OTP found for this user');
    }

    if (user.otpExpires < new Date()) {
      throw new BadRequestException('OTP has expired');
    }

    if (user.otp !== otp) {
      throw new BadRequestException('Invalid OTP');
    }

    // Update user as verified and clear OTP
    await this.databaseService.user.update({
      where: { id: user.id },
      data: {
        isVerified: true,
        otp: null,
        otpExpires: null,
        lastLoginAt: new Date(),
      }
    });

    // Generate JWT token
    const payload = {
      sub: user.id,
      email: user.email,
      phone: user.phone,
      username: user.username,
      role: user.role
    };
    const accessToken = this.jwtService.sign(payload);

    return {
      message: 'OTP verified successfully',
      accessToken,
      user: {
        id: user.id,
        email: user.email,
        phone: user.phone,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isVerified: true,
      }
    };
  }

  async setPassword(setPasswordDto: SetPasswordDto, userId: number) {
    const { password } = setPasswordDto;

    const user = await this.databaseService.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 12);

    await this.databaseService.user.update({
      where: { id: userId },
      data: {
        password: hashedPassword,
      }
    });

    return {
      message: 'Password set successfully',
    };
  }

  private generateOtp(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // TODO: Implement email service
  // private async sendOtpEmail(email: string, otp: string) {
  //   // Implementation for sending OTP email
  // }

  // TODO: Implement SMS service
  // private async sendOtpSms(phone: string, otp: string) {
  //   // Implementation for sending OTP SMS
  // }
}
