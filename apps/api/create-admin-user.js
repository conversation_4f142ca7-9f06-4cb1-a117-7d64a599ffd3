const { PrismaClient } = require('./generated/prisma');

const prisma = new PrismaClient();

async function createAdminUser() {
  try {
    // Check if admin user already exists
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('Admin user already exists:', existingAdmin.email || existingAdmin.phone);
      return;
    }

    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        role: 'ADMIN',
        isActive: true,
        isVerified: true,
        country: 'UG'
      }
    });

    console.log('Admin user created successfully:');
    console.log('Email:', adminUser.email);
    console.log('Username:', adminUser.username);
    console.log('Role:', adminUser.role);
    console.log('\nYou can now login to the dashboard using:');
    console.log('Email: <EMAIL>');
    console.log('Country: UG (Uganda)');
    console.log('The OTP will be displayed in the API response for testing.');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
