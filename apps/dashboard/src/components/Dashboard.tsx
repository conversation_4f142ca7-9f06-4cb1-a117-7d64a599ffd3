import React from 'react';
import { useAuthStore } from '../stores/authStore';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuthStore();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                Admin Dashboard
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">
                    {user?.firstName?.charAt(0) || user?.username?.charAt(0) || 'A'}
                  </span>
                </div>
                <div className="text-sm">
                  <p className="text-gray-900 font-medium">
                    {user?.firstName && user?.lastName 
                      ? `${user.firstName} ${user.lastName}` 
                      : user?.username}
                  </p>
                  <p className="text-gray-500">{user?.email || user?.phone}</p>
                </div>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Welcome to the Admin Dashboard
              </h2>
              <p className="text-gray-600 mb-6">
                You have successfully logged in with admin privileges.
              </p>
              
              {/* User Info Card */}
              <div className="bg-white rounded-lg shadow p-6 max-w-md mx-auto">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Your Profile
                </h3>
                <div className="space-y-2 text-left">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Username:</span>
                    <span className="text-gray-900">{user?.username}</span>
                  </div>
                  {user?.email && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Email:</span>
                      <span className="text-gray-900">{user.email}</span>
                    </div>
                  )}
                  {user?.phone && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Phone:</span>
                      <span className="text-gray-900">{user.phone}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-500">Role:</span>
                    <span className="text-indigo-600 font-medium">{user?.role}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Status:</span>
                    <span className={`font-medium ${user?.isVerified ? 'text-green-600' : 'text-yellow-600'}`}>
                      {user?.isVerified ? 'Verified' : 'Pending Verification'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white rounded-lg shadow p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Users</h4>
                  <p className="text-gray-600 text-sm">Manage user accounts</p>
                  <button className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    View Users →
                  </button>
                </div>
                
                <div className="bg-white rounded-lg shadow p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Analytics</h4>
                  <p className="text-gray-600 text-sm">View system analytics</p>
                  <button className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    View Analytics →
                  </button>
                </div>
                
                <div className="bg-white rounded-lg shadow p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Settings</h4>
                  <p className="text-gray-600 text-sm">System configuration</p>
                  <button className="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                    Open Settings →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
