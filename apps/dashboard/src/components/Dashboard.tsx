import React from 'react';
import Layout from './layout/Layout';
import StatsCard from './dashboard/StatsCard';
import ChartCard from './dashboard/ChartCard';
import SimpleChart from './dashboard/SimpleChart';
import RecentActivity from './dashboard/RecentActivity';

const Dashboard: React.FC = () => {
  // Sample data
  const statsData = [
    {
      title: 'Total Users',
      value: '3,782',
      change: '11.01%',
      changeType: 'increase' as const,
      color: 'blue' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
        </svg>
      ),
    },
    {
      title: 'Total Orders',
      value: '5,359',
      change: '9.05%',
      changeType: 'increase' as const,
      color: 'green' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 12a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      title: 'Revenue',
      value: '$45,678',
      change: '2.3%',
      changeType: 'decrease' as const,
      color: 'yellow' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z" />
          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd" />
        </svg>
      ),
    },
    {
      title: 'Growth',
      value: '12.5%',
      change: '1.8%',
      changeType: 'increase' as const,
      color: 'red' as const,
      icon: (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
        </svg>
      ),
    },
  ];

  const chartData = [65, 78, 66, 44, 56, 67, 75, 82, 94, 89, 76, 85];
  const chartLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  const recentActivities = [
    {
      id: '1',
      user: 'John Doe',
      action: 'Created new user',
      target: 'User Management',
      time: '2 minutes ago',
      status: 'success' as const,
    },
    {
      id: '2',
      user: 'Jane Smith',
      action: 'Updated profile',
      target: 'Profile Settings',
      time: '5 minutes ago',
      status: 'success' as const,
    },
    {
      id: '3',
      user: 'Mike Johnson',
      action: 'Failed login attempt',
      target: 'Authentication',
      time: '10 minutes ago',
      status: 'failed' as const,
    },
    {
      id: '4',
      user: 'Sarah Wilson',
      action: 'Pending verification',
      target: 'Account Verification',
      time: '15 minutes ago',
      status: 'pending' as const,
    },
  ];

  return (
    <Layout>
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your platform.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statsData.map((stat, index) => (
          <StatsCard
            key={index}
            title={stat.title}
            value={stat.value}
            change={stat.change}
            changeType={stat.changeType}
            color={stat.color}
            icon={stat.icon}
          />
        ))}
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <ChartCard
          title="Monthly Sales"
          subtitle="Sales performance over the last 12 months"
          actions={
            <>
              <button className="text-sm text-gray-500 hover:text-gray-700">View More</button>
              <button className="text-sm text-red-500 hover:text-red-700">Delete</button>
            </>
          }
        >
          <SimpleChart data={chartData} labels={chartLabels} height={300} />
        </ChartCard>

        <ChartCard
          title="Monthly Target"
          subtitle="Target you've set for each month"
          actions={
            <>
              <button className="text-sm text-gray-500 hover:text-gray-700">View More</button>
              <button className="text-sm text-red-500 hover:text-red-700">Delete</button>
            </>
          }
        >
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Target</span>
              <span className="text-lg font-semibold text-gray-900">$20K</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Revenue</span>
              <span className="text-lg font-semibold text-gray-900">$20K</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Today</span>
              <span className="text-lg font-semibold text-gray-900">$20K</span>
            </div>
            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-green-800">
                    You earn $3287 today, it's higher than last month. Keep up your good work!
                  </p>
                </div>
              </div>
            </div>
          </div>
        </ChartCard>
      </div>

      {/* Recent Activity */}
      <RecentActivity activities={recentActivities} />
    </Layout>
  );
};

export default Dashboard;
