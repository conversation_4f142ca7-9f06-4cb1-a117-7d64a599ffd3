import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface User {
  id: number;
  email?: string;
  phone?: string;
  username: string;
  firstName?: string;
  lastName?: string;
  role: string;
  isVerified: boolean;
}

interface RegisterData {
  username: string;
  firstName?: string;
  lastName?: string;
  country?: string;
  bio?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  loading: boolean;
  
  // Actions
  login: (email: string, phone: string, country: string) => Promise<{ isNewUser: boolean; otp?: string }>;
  verifyOtp: (email: string, phone: string, otp: string) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      loading: false,

      login: async (email: string, phone: string, country: string) => {
        set({ loading: true });
        try {
          const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, phone, country }),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Login failed');
          }

          const data = await response.json();
          return data;
        } catch (error) {
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      verifyOtp: async (email: string, phone: string, otp: string) => {
        set({ loading: true });
        try {
          const response = await fetch('/api/auth/verify-otp', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, phone, otp }),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'OTP verification failed');
          }

          const data = await response.json();
          
          // Check if user has admin role
          if (data.user.role !== 'ADMIN') {
            throw new Error('Access denied. Admin role required.');
          }

          set({ 
            token: data.accessToken, 
            user: data.user 
          });
        } catch (error) {
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      register: async (userData: RegisterData) => {
        const { token } = get();
        if (!token) {
          throw new Error('Authentication required');
        }

        set({ loading: true });
        try {
          const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify(userData),
          });

          if (!response.ok) {
            const error = await response.json();
            throw new Error(error.message || 'Registration failed');
          }

          const data = await response.json();
          set({ user: data.user });
        } catch (error) {
          throw error;
        } finally {
          set({ loading: false });
        }
      },

      logout: () => {
        set({ user: null, token: null });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        token: state.token 
      }),
    }
  )
);
