import { useEffect, useState } from 'react'

function App() {
  const [msg, setMsg] = useState('');

  useEffect(() =>{
    fetch("/api")
    .then((res) => res.text())
    .then((data) => {
      setMsg(data);
    }).catch((err) => {
      console.log(err.message);
    })
  },[])

  return (
    <div className="min-h-screen bg-red-500 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-md">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">Dashboard</h1>
        <p className="text-gray-600">{msg || 'Loading...'}</p>
      </div>
    </div>
  )
}

export default App
